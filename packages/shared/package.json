{"name": "@magic-partner/shared", "version": "1.0.0", "description": "Shared utilities for magic-partner monorepo", "main": "index.js", "type": "module", "exports": {"./stores": "./src/stores/index.js", "./utils": "./src/utils/index.js", "./types": "./src/types/index.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch"}, "dependencies": {"pinia": "^2.1.7", "vue": "^3.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0"}}