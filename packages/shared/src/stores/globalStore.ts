import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface User {
  id: string
  name: string
  email?: string
  avatar?: string
  coins?: number
  isGuest?: boolean
}

export interface AuthState {
  token?: string
  refreshToken?: string
  sessionId?: string
  isAuthenticated: boolean
}

export interface StoryState {
  currentStoryId?: string
  currentActorId?: string
  chatType?: 'chat' | 'chat2' | 'chat3' | 'chat4'
}

/**
 * 全局共享状态store
 * 在主应用和子应用间共享状态
 */
export const useGlobalStore = defineStore('global', () => {
  // 用户状态
  const user = ref<User | null>(null)
  const auth = ref<AuthState>({
    isAuthenticated: false
  })
  
  // 故事状态
  const story = ref<StoryState>({})
  
  // 应用状态
  const isLoading = ref(false)
  const currentApp = ref<string>('nuxt-app')
  
  // 计算属性
  const isAuthenticated = computed(() => auth.value.isAuthenticated)
  const isGuest = computed(() => user.value?.isGuest || false)
  
  // Actions
  const setUser = (userData: User | null) => {
    user.value = userData
    // 广播用户状态变化
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('user-updated', { 
        detail: userData 
      }))
    }
  }
  
  const setAuth = (authData: AuthState) => {
    auth.value = authData
    // 广播认证状态变化
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('auth-updated', { 
        detail: authData 
      }))
    }
  }
  
  const setStory = (storyData: StoryState) => {
    story.value = { ...story.value, ...storyData }
    // 广播故事状态变化
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('story-updated', { 
        detail: story.value 
      }))
    }
  }
  
  const setCurrentApp = (appName: string) => {
    currentApp.value = appName
  }
  
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }
  
  // 同步状态到其他应用
  const syncState = () => {
    return {
      user: user.value,
      auth: auth.value,
      story: story.value,
      currentApp: currentApp.value,
      isLoading: isLoading.value
    }
  }
  
  // 从其他应用接收状态
  const receiveState = (state: any) => {
    if (state.user !== undefined) user.value = state.user
    if (state.auth !== undefined) auth.value = state.auth
    if (state.story !== undefined) story.value = state.story
    if (state.currentApp !== undefined) currentApp.value = state.currentApp
    if (state.isLoading !== undefined) isLoading.value = state.isLoading
  }
  
  return {
    // State
    user,
    auth,
    story,
    isLoading,
    currentApp,
    
    // Computed
    isAuthenticated,
    isGuest,
    
    // Actions
    setUser,
    setAuth,
    setStory,
    setCurrentApp,
    setLoading,
    syncState,
    receiveState
  }
})

export type GlobalStore = ReturnType<typeof useGlobalStore>
